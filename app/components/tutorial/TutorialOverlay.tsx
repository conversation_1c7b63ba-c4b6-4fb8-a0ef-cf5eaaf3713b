'use client';

import { useState, useEffect, createContext, useContext } from 'react';
import { useRouter } from 'next/navigation';
import {
  BuildingStorefrontIcon,
  CalendarIcon,
  ChatBubbleLeftIcon,
  BanknotesIcon,
  BoltIcon,
  PaintBrushIcon,
  LinkIcon,
  XMarkIcon,
  UserCircleIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { rtdb } from '@/lib/firebase';
import { ref, get, set } from 'firebase/database';
import { useAuth } from '@/app/contexts/AuthContext';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
}

interface TutorialContextType {
  activateTutorial: () => void;
}

const TutorialContext = createContext<TutorialContextType | undefined>(undefined);

export function useTutorial() {
  const context = useContext(TutorialContext);
  if (!context) {
    throw new Error('useTutorial must be used within a TutorialProvider');
  }
  return context;
}

export function TutorialBanner() {
  const { user } = useAuth();
  const [showBanner, setShowBanner] = useState(false); // Inicializar como false para evitar parpadeo
  const [tutorialComplete, setTutorialComplete] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Usando el contexto de tutorial
  const { activateTutorial } = useTutorial();

  // Verificar si el usuario ha completado el tutorial o lo ha pospuesto
  useEffect(() => {
    const checkTutorialStatus = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Primero comprobar si el usuario ha pospuesto el tutorial temporalmente (24h)
        const postponedUntil = localStorage.getItem('tutorial_postponed');
        if (postponedUntil) {
          const postponedDate = new Date(postponedUntil);
          const now = new Date();
          console.log('Verificando tutorial pospuesto:', {
            postponedUntil: postponedDate.toLocaleString(),
            now: now.toLocaleString(),
            shouldHide: postponedDate > now
          });

          if (postponedDate > now) {
            // Todavía estamos dentro del período de posposición
            console.log('Tutorial pospuesto, no mostrar banner');
            setShowBanner(false);
            setTutorialComplete(false);
            setIsLoading(false);
            return;
          } else {
            // Si ya pasaron las 24 horas, limpiar el localStorage
            localStorage.removeItem('tutorial_postponed');
            console.log('Período de posposición expirado, verificando preferencias en Firebase');
          }
        }

        // Comprobar si el usuario ha marcado el tutorial como completado (no quiere verlo más)
        const tutorialRef = ref(rtdb, `users/${user.uid}/tutorialComplete`);
        const snapshot = await get(tutorialRef);

        if (snapshot.exists() && snapshot.val() === true) {
          // El usuario completó el tutorial y NO quiere verlo de nuevo nunca
          console.log('Tutorial marcado como completado permanentemente, no se mostrará más');
          setTutorialComplete(true);
          setShowBanner(false);
        } else if (snapshot.exists() && snapshot.val() === false) {
          // El usuario ha completado el tutorial pero quiere verlo de nuevo
          console.log('Usuario completó el tutorial pero quiere verlo de nuevo');
          setTutorialComplete(false);
          setShowBanner(true);
        } else {
          // Primera vez del usuario, mostrar el tutorial
          console.log('Primera vez del usuario, mostrando banner del tutorial');
          setTutorialComplete(false);
          setShowBanner(true);
        }
      } catch (error) {
        console.error('Error checking tutorial status:', error);
        // En caso de error, mostrar el tutorial por defecto
        setShowBanner(true);
        setTutorialComplete(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkTutorialStatus();
  }, [user]);

  // Función para posponer el tutorial por 24 horas
  const postponeTutorial = () => {
    const tomorrow = new Date();
    tomorrow.setTime(tomorrow.getTime() + (24 * 60 * 60 * 1000)); // Más preciso que setHours
    localStorage.setItem('tutorial_postponed', tomorrow.toISOString());
    console.log('Tutorial pospuesto hasta:', tomorrow.toLocaleString());

    // Actualizar estados inmediatamente para evitar parpadeo
    setShowBanner(false);
    setTutorialComplete(false); // No está completado permanentemente, solo pospuesto
  };

  // Función para no mostrar más el tutorial (permanentemente)
  const dismissTutorialPermanently = async () => {
    if (!user) return;

    try {
      const tutorialRef = ref(rtdb, `users/${user.uid}/tutorialComplete`);
      await set(tutorialRef, true); // Marcar como completado (no mostrar más)
      console.log('Tutorial marcado como completado permanentemente');

      // Actualizar estados inmediatamente
      setTutorialComplete(true);
      setShowBanner(false);

      // También limpiar cualquier posposición temporal
      localStorage.removeItem('tutorial_postponed');
    } catch (error) {
      console.error('Error al marcar tutorial como completado:', error);
    }
  };

  // Si está cargando, no mostrar nada aún
  if (isLoading || !user) return null;

  // Si el usuario ya completó el tutorial o el banner está oculto, no mostrar nada
  if (tutorialComplete || !showBanner) return null;

  return (
    <div className="bg-gradient-to-r from-indigo-600 to-violet-600 text-white rounded-lg mb-6 shadow-xl overflow-hidden animate-fadeIn">
      <div className="relative">
        {/* Efectos de fondo */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-24 -left-24 w-48 h-48 rounded-full bg-white/10 blur-xl"></div>
          <div className="absolute -bottom-24 -right-24 w-48 h-48 rounded-full bg-white/10 blur-xl"></div>
        </div>

        {/* Layout responsivo con mejor distribución para todas las resoluciones */}
        <div className="relative px-6 py-5 grid grid-cols-1 md:grid-cols-12 gap-6 items-center">
          {/* Columna de contenido - toma 7/12 en desktop */}
          <div className="flex items-center gap-4 md:col-span-7">
            <div className="bg-white/20 p-3 rounded-full shadow-inner flex-shrink-0">
              <SparklesIcon className="h-6 w-6 text-yellow-200" />
            </div>
            <div>
              <h3 className="font-bold text-xl mb-1">¡Bienvenido a Tatu.Ink!</h3>
              <p className="text-white/90 text-sm md:text-base">Haz clic en "Primeros pasos" para descubrir todas las funcionalidades disponibles.</p>
            </div>
          </div>

          {/* Columna de botones - toma 5/12 en desktop */}
          <div className="flex items-center justify-end gap-3 md:col-span-5">
            <button 
              className="group text-white/70 hover:text-white text-sm px-3 py-2 rounded-lg hover:bg-white/10 transition-all duration-200 flex items-center gap-2" 
              onClick={postponeTutorial}
            >
              <span className="hidden md:inline">Recordar en</span> 24h
            </button>
            <button 
              onClick={() => {
                activateTutorial();
                setShowBanner(false);
              }}
              className="bg-white hover:bg-white/90 text-indigo-700 font-medium px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              Primeros pasos
              <span className="hidden md:inline">→</span>
            </button>
          </div>
        </div>
      </div>
      <div className="h-1 w-full bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-500"></div>
    </div>
  );
}

export function TutorialProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const router = useRouter();
  const [showTutorial, setShowTutorial] = useState(false);
  const [showFinalStep, setShowFinalStep] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [showTutorialAgain, setShowTutorialAgain] = useState(true); // Por defecto true para mostrar el tutorial

  // Activar tutorial
  const activateTutorial = () => {
    setShowTutorial(true);
    setCurrentStep(0);
    setShowFinalStep(false);
  };

  // Valor del contexto
  const contextValue = {
    activateTutorial
  };

  const tutorialSteps: TutorialStep[] = [
    {
      id: 'studio',
      title: 'Estudio',
      description: 'Tu dashboard principal donde encontrarás un resumen de tu día: citas programadas, ingresos recientes, notificaciones pendientes y acciones rápidas. Desde aquí puedes ver de un vistazo todo lo que necesitas para gestionar tu negocio diariamente y acceder rápidamente a tus herramientas más utilizadas.',
      icon: BuildingStorefrontIcon
    },
    {
      id: 'calendar',
      title: 'Calendario',
      description: 'Gestiona tu agenda completa y disponibilidad. Aquí podrás ver, crear y modificar citas con tus clientes, bloquear horas no disponibles, configurar recordatorios automáticos, y visualizar tu ocupación semanal o mensual. Los clientes pueden reservar directamente en los espacios que hayas marcado como disponibles.',
      icon: CalendarIcon
    },
    {
      id: 'messages',
      title: 'Mensajes',
      description: 'Central de comunicación con tus clientes. Recibe consultas, envía presupuestos, comparte diseños y mantén conversaciones organizadas por cliente. Incluye plantillas predefinidas para respuestas frecuentes, notificaciones de mensajes nuevos y la posibilidad de adjuntar archivos e imágenes para compartir tu trabajo previo.',
      icon: ChatBubbleLeftIcon
    },
    {
      id: 'finances',
      title: 'Finanzas',
      description: 'Control completo de tu economía: registra ingresos, gastos y depósitos de reserva. Genera informes por períodos (diario, semanal, mensual o anual), visualiza gráficos de rendimiento, configura recordatorios de pagos pendientes y exporta datos para tu contabilidad. Te ayuda a tener claras tus ganancias y planificar mejor.',
      icon: BanknotesIcon
    },
    {
      id: 'assistant',
      title: 'Asistente Virtual',
      description: 'Tu ayudante personal con IA que puede responder preguntas frecuentes de clientes, gestionar consultas iniciales y reservas básicas. Puedes entrenar al asistente con tus propias respuestas, personalizar su tono de voz y configurar cuándo debe intervenir y cuándo derivar la conversación a ti. Funciona 24/7, incluso cuando no estás disponible.',
      icon: SparklesIcon
    },
    {
      id: 'automations',
      title: 'Automatizaciones',
      description: 'Centro de control para optimizar tareas repetitivas. Configura flujos automáticos como: envío de recordatorios de citas, seguimiento post-servicio, solicitud de reseñas, felicitaciones en fechas especiales, o respuestas automáticas cuando estás ocupado. Cada automatización puede personalizarse y programarse según tus necesidades específicas.',
      icon: BoltIcon
    },
    {
      id: 'customization',
      title: 'Personalización',
      description: 'Adapta la apariencia y funcionalidad de tu espacio digital. Personaliza colores, tipografías y estilos de tu perfil público, configura las secciones que deseas mostrar a tus clientes, sube ejemplos de tu trabajo y testimonios destacados. Aquí también puedes editar tu biografía, información de contacto y especialidades.',
      icon: PaintBrushIcon
    },
    {
      id: 'links',
      title: 'Enlaces',
      description: 'Gestiona tu presencia online con links personalizados para compartir en redes sociales. Crea y monitorea enlaces cortos tipo tatu.ink/tunombre, integra botones de acción (reservar cita, consulta, etc.), conecta tus perfiles de Instagram, TikTok y otras plataformas, y analiza qué canales te traen más clientes potenciales.',
      icon: LinkIcon
    },
    {
      id: 'profile',
      title: 'Perfil',
      description: 'Administra tu información personal, credenciales profesionales y configuración de cuenta. Actualiza tu foto de perfil, datos de contacto, horarios de trabajo, ubicación del estudio y métodos de pago aceptados. También puedes configurar tus preferencias de notificaciones, privacidad y gestionar el acceso a tu cuenta.',
      icon: UserCircleIcon
    }
  ];

  useEffect(() => {
    if (user) {
      loadTutorialPreference();
    }
  }, [user]);

  const loadTutorialPreference = async () => {
    if (!user) return;

    try {
      const tutorialRef = ref(rtdb, `users/${user.uid}/tutorialComplete`);
      const snapshot = await get(tutorialRef);

      if (snapshot.exists()) {
        // Si tutorialComplete es true, el usuario NO quiere ver el tutorial (showTutorialAgain = false)
        // Si tutorialComplete es false, el usuario SÍ quiere ver el tutorial (showTutorialAgain = true)
        const tutorialComplete = snapshot.val();
        setShowTutorialAgain(!tutorialComplete);
        console.log('Tutorial preference loaded:', { tutorialComplete, showTutorialAgain: !tutorialComplete });
      } else {
        // Primera vez del usuario, por defecto mostrar tutorial
        setShowTutorialAgain(true);
        console.log('First time user, will show tutorial by default');
      }
    } catch (error) {
      console.error('Error loading tutorial preference:', error);
    }
  };

  const saveTutorialPreference = async () => {
    if (!user) return;

    try {
      const tutorialRef = ref(rtdb, `users/${user.uid}/tutorialComplete`);
      // Si showTutorialAgain es true, NO marcar como completado (false)
      // Si showTutorialAgain es false, SÍ marcar como completado (true)
      const tutorialComplete = !showTutorialAgain;
      await set(tutorialRef, tutorialComplete);
      console.log('Tutorial preference saved:', { showTutorialAgain, tutorialComplete });
    } catch (error) {
      console.error('Error saving tutorial preference:', error);
    }
  };

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setShowFinalStep(true);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinish = async () => {
    try {
      await saveTutorialPreference();
      // También limpiar cualquier posposición temporal
      localStorage.removeItem('tutorial_postponed');
    } catch (error) {
      console.error('Error al guardar preferencia:', error);
    } finally {
      // Siempre cerrar el tutorial independientemente de si hubo errores
      setShowTutorial(false);
      setShowFinalStep(false);

      // Si estamos en la página de inicio, redirigir al dashboard
      if (window.location.pathname === '/') {
        router.push('/studio');
      }
    }
  };

  const handleSkip = async () => {
    // Cuando el usuario cierra el tutorial sin completarlo,
    // por defecto no mostrar más el tutorial (como si lo hubiera completado)
    setShowTutorialAgain(false);

    try {
      await saveTutorialPreference();
      // También limpiar cualquier posposición temporal
      localStorage.removeItem('tutorial_postponed');
    } catch (error) {
      console.error('Error al guardar preferencia:', error);
    }

    setShowTutorial(false);
    setShowFinalStep(false);
  };

  if (showFinalStep) {
    return (
      <TutorialContext.Provider value={contextValue}>
        {children}
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl shadow-xl p-8 max-w-md w-full">
            <div className="text-center mb-6">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900">¡Tutorial completado!</h2>
              <p className="text-gray-600 mt-3 mb-6">
                Ya conoces todas las secciones principales de tu estudio. ¡Estás listo para comenzar!
              </p>
            </div>

            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-blue-800 mb-1">¿Necesitas ayuda adicional?</h3>
              <p className="text-blue-700 text-sm">
                Si tienes más dudas, escríbenos a <a href="mailto:<EMAIL>" className="underline font-medium"><EMAIL></a> y te ayudaremos con todo lo que necesites.
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="show-tutorial-again"
                  checked={showTutorialAgain}
                  onChange={() => setShowTutorialAgain(!showTutorialAgain)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="show-tutorial-again" className="ml-2 block text-sm text-gray-700">
                  Mostrar este tutorial la próxima vez que inicie sesión
                </label>
              </div>

              <button
                onClick={handleFinish}
                className="w-full py-3 px-4 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium transition-colors"
              >
                Entendido, ¡comencemos!
              </button>
            </div>
          </div>
        </div>
      </TutorialContext.Provider>
    );
  }

  const currentTutorialStep = tutorialSteps[currentStep];
  const Icon = currentTutorialStep.icon;

  return (
    <TutorialContext.Provider value={contextValue}>
      {children}
      {showTutorial && (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl shadow-2xl p-6 max-w-lg w-full transition-all duration-300 transform">
            <div className="flex justify-between items-start mb-4">
              <div className="bg-indigo-100 p-3 rounded-lg inline-block">
                <Icon className="h-8 w-8 text-indigo-600" />
              </div>
              <button
                className="text-gray-400 hover:text-gray-600 transition-colors"
                onClick={handleSkip}
              >
                <XMarkIcon className="h-6 w-6" />
                <span className="sr-only">Cerrar tutorial</span>
              </button>
            </div>

            <h3 className="font-bold text-xl text-gray-900 mt-2">{currentTutorialStep.title}</h3>
            <p className="text-gray-600 mt-2 text-base leading-relaxed mb-6">{currentTutorialStep.description}</p>

            <div className="mt-8 flex items-center justify-between">
              <div className="flex space-x-1">
                {tutorialSteps.map((_, index) => (
                  <div
                    key={index}
                    className={`h-1.5 rounded-full w-4 transition-colors ${
                      index === currentStep ? 'bg-indigo-600' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>

              <div className="flex space-x-3">
                {currentStep > 0 && (
                  <button
                    onClick={handlePrevious}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Anterior
                  </button>
                )}

                <button
                  onClick={handleNext}
                  className="px-5 py-2 bg-indigo-600 rounded-lg text-sm text-white hover:bg-indigo-700 transition-colors"
                >
                  {currentStep === tutorialSteps.length - 1 ? 'Finalizar' : 'Siguiente'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </TutorialContext.Provider>
  );
}

// Este componente simplemente renderiza el contenido del tutorial activado por el banner
export default function TutorialOverlay() {
  return null; // Ya no necesitamos renderizar nada aquí, todo se maneja en el Provider
}